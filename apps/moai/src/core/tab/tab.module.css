.full {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.titles {
  flex: 0 0 auto;
  padding: 0 16px;
  /* So this can hide .content */
  position: relative;
  z-index: var(--z-base);
  display: flex;
}

.title {
  padding: 8px 12px;
  cursor: pointer;
  transition: outline 0.2s;
}

.content {
  flex: 1 1 0px;
  overflow: hidden;
}

/* OUTSET */

.outsetContent {
  /* Allow .titles to overlap */
  position: relative;
  top: -1px;
  /* Because children may have shadow */
  padding: 8px;
  margin: -8px;
}

.outsetTitle {
  border-style: solid;
  border-width: 1px;
  border-bottom: none;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.outsetInactive {
  border-color: transparent;
}

/* Flat */

.flatPadding {
  padding: 16px;
}

.flatFullHeight {
  height: 100%;
}

.flatContent {
  border-top-style: solid;
  border-top-width: 2px;
  margin-top: -2px;
}

.flatTitle {
  border: none; /* Reset default border of buttons */
  border-bottom-style: solid;
  border-bottom-width: 2px;
}

.flatInactive {
  border-bottom-color: transparent;
}

.flatActive {
  border-bottom-color: var(--highlight-5);
}
