import { Meta } from "@storybook/react";
import { Button, Dialog, EmptyState } from "../../core";

const meta: Meta = {
  title: "Draft/Empty",
  component: EmptyState,
};

export default meta;

export const Primary: StoryObj<typeof EmptyState> = {
  render: () => {
    return (
      <div>
				<EmptyState message="Error connection, please wait." />
				<br />
				<EmptyState
					message="Error connection, please try again later"
					action={
						<Button
							onClick={() => {
								Dialog.alert("Retry the connection !!!");
							}}
							highlight
						>
							Retry
						</Button>
					}
				/>
			</div>
    );
  }
};
